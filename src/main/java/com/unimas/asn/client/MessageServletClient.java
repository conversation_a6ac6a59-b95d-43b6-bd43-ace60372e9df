package com.unimas.asn.client;


import com.unimas.asn.codec.OssServAdapter;
import com.unimas.asn.servicemanager.servicemanagementhttp.*;
import com.unimas.asn.util.IPUtil;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Scanner;

/**
 * MessageServlet的交互式测试客户端
 * 支持从终端选择测试不同的接口功能
 */
public class MessageServletClient {
    private static Scanner scanner = new Scanner(System.in);
    private static HttpClient client;

    public static void main(String[] args) {
        try {
            System.out.println("=== MessageServlet 测试客户端 ===");

            // 获取服务器地址
            System.out.print("请输入服务器地址 (例如 http://localhost:8080): ");
            String serverUrl = scanner.nextLine().trim();
            client = new HttpClient(serverUrl);

            boolean exit = false;
            while (!exit) {
                printMainMenu();

                try {
                    int choice = Integer.parseInt(scanner.nextLine().trim());

                    switch (choice) {
                        case 1:
                            handleServiceConfigMenu();
                            break;
                        case 2:
                            handleServiceControl();
                            break;
                        case 3:
                            handleServiceStatusQuery();
                            break;
                        case 4:
                            handleServiceInfoQuery();
                            break;
                        case 5:
                            handleHostStatusQuery();
                            break;
                        case 6:
                            handleGetAllServiceIds();
                            break;
                        case 7:
                            handleCheckCommStatus();
                            break;
                        case 8:
                            handleSendPacketStats();
                            break;
                        case 9:
                            handleReceivePacketStats();
                            break;
                        case 11:
                            handleSetInterfaceIp();
                            break;
                        case 12:
                            handleQueryHostMng();
                            break;
                        case 13:
                            handleSetHostMng();
                            break;
                        case 14:
                            handleQuerySourceDevice();
                            break;
                        case 15:
                            handleSetSourceDevice();
                            break;
                        case 0:
                            exit = true;
                            break;
                        default:
                            System.out.println("无效选择，请重试");
                    }
                } catch (NumberFormatException e) {
                    System.out.println("请输入有效的数字");
                }
            }

            System.out.println("测试客户端已退出");
        } finally {
            if (scanner != null) {
                scanner.close();
            }
        }
    }

    /**
     * 打印主菜单
     */
    private static void printMainMenu() {
        System.out.println("\n请选择要测试的接口:");
        System.out.println("1. 服务配置接口 (创建/删除/更新)");
        System.out.println("2. 服务启停接口");
        System.out.println("3. 服务状态查询接口");
        System.out.println("4. 服务信息查询接口");
        System.out.println("5. 主机状态查询接口");
        System.out.println("6. 获取所有服务ID接口");
        System.out.println("7. 检查通信状态接口");
        System.out.println("8. 发送包统计查询接口");
        System.out.println("9. 接收包统计查询接口");
        System.out.println("10. 设置管理配置接口 (已废弃)");
        System.out.println("11. 设置网口IP接口");
        System.out.println("12. 查询上位机管理接口");
        System.out.println("13. 设置上位机管理接口");
        System.out.println("14. 查询源设备配置接口");
        System.out.println("15. 设置源设备配置接口");
        System.out.println("0. 退出");
        System.out.print("请输入选择: ");
    }

    /**
     * 处理服务配置菜单
     */
    private static void handleServiceConfigMenu() {
        System.out.println("\n服务配置接口:");
        System.out.println("1. 创建服务");
        System.out.println("2. 删除服务");
        System.out.println("3. 更新服务");
        System.out.println("0. 返回主菜单");
        System.out.print("请输入选择: ");

        try {
            int choice = Integer.parseInt(scanner.nextLine().trim());

            switch (choice) {
                case 1:
                    handleCreateService();
                    break;
                case 2:
                    handleDeleteService();
                    break;
                case 3:
                    handleUpdateService();
                    break;
                case 0:
                    return;
                default:
                    System.out.println("无效选择，请重试");
            }
        } catch (NumberFormatException e) {
            System.out.println("请输入有效的数字");
        }
    }

    /**
     * 处理创建服务请求
     */
    private static void handleCreateService() {
        try {
            // 获取服务显示名称
            System.out.print("请输入服务显示名称: ");
            String displayName = scanner.nextLine().trim();
            byte b [] = new byte[40];
            System.arraycopy(displayName.getBytes(),0,b,10,displayName.getBytes().length);

            // 获取网络类型
            System.out.println("请选择网络类型:");
            System.out.println("0. 发送端 (SENDER)");
            System.out.println("1. 接收端 (RECEIVER)");
            System.out.print("请输入选择: ");
            int networkChoice = Integer.parseInt(scanner.nextLine().trim());
            Network network = (networkChoice == 0) ? Network.sender : Network.receiver;

            // 创建添加服务请求
            AddServiceRequest addRequest = new AddServiceRequest();
            addRequest.setMessageType(ContentMessageType.addService);
            addRequest.setDisplayname(new DisplayName(b));
            addRequest.setNetwork(network);

            if (network == Network.sender) {
                // 发送端特有字段
                System.out.print("请输入代理IP: ");
                String proxyIp = scanner.nextLine().trim();
                addRequest.setProxyIp(IPUtil.createIPAddress(proxyIp));

                System.out.print("请输入代理端口: ");
                int proxyPort = Integer.parseInt(scanner.nextLine().trim());
                addRequest.setProxyPort(new PortNumber(proxyPort));

                // 设置关键字检测位图
                System.out.print("请输入内容关键字检测位图值 (十六进制，例如 03): ");
                String keyCheckHex = scanner.nextLine().trim();
                byte[] contentKeyCheck = new byte[1];
                contentKeyCheck[0] = (byte) Integer.parseInt(keyCheckHex, 16);
                addRequest.setContentKeyCheck(new ContentKeyCheck(contentKeyCheck));

                // 设置协议过滤位图
                System.out.print("请输入协议过滤位图值 (十六进制，例如 03): ");
                String protocolFilterHex = scanner.nextLine().trim();
                byte[] protocolFilter = new byte[1];
                protocolFilter[0] = (byte) Integer.parseInt(protocolFilterHex, 16);
                addRequest.setProtocolFilter(new ProtocolFilter(protocolFilter));

                // 设置告警后处理
                System.out.println("请选择未通过处理方式:");
                System.out.println("0. ALLOW");
                System.out.println("1. DENY");
                System.out.print("请输入选择: ");
                int dealChoice = Integer.parseInt(scanner.nextLine().trim());
                PermissionState unpassDeal;
                switch (dealChoice) {
                    case 0:
                        unpassDeal = PermissionState.allow;
                        break;
                    case 1:
                        unpassDeal = PermissionState.forbidden;
                        break;
                    default:
                        unpassDeal = PermissionState.allow;
                }
                addRequest.setUnpassDeal(unpassDeal);
            } else {
                // 接收端特有字段
                System.out.print("请输入服务器IP: ");
                String serverIp = scanner.nextLine().trim();
                addRequest.setServerIp(IPUtil.createIPAddress(serverIp));

                System.out.print("请输入服务器端口: ");
                int serverPort = Integer.parseInt(scanner.nextLine().trim());
                addRequest.setServerPort(new PortNumber(serverPort));
            }

            // 创建服务配置请求
            ServiceConfigRequest configRequest = new ServiceConfigRequest();
            configRequest.setAddServiceRequest(addRequest);

            // 发送请求并处理响应
            // 创建消息帧
            MessageRequestFrame frame = new MessageRequestFrame();
            frame.setVersion(new Uint8(1));
            frame.setContent(new MessageRequestFrame.Content());
            frame.getContent().setServiceConfigRequest(configRequest);
            MessageResponseFrame responseFrame = sendRequest(frame);

            // 输出响应信息
            if (responseFrame.getContent().hasServiceConfigResponse()) {
                ServiceConfigResponse response = responseFrame.getContent().getServiceConfigResponse();
                System.out.println("服务创建成功，服务ID: " + response.getServiceId());
            } else if (responseFrame.getContent().hasError()) {
                ErrorResponse errorResponse = responseFrame.getContent().getError();
                System.out.println("请求失败: " + errorResponse.getErrorState());
            }
        } catch (Exception e) {
            System.err.println("创建服务失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    /**
     * 处理删除服务请求
     */
    private static void handleDeleteService() {
        try {
            // 获取要删除的服务ID
            System.out.print("请输入要删除的服务ID: ");
            int serviceId = Integer.parseInt(scanner.nextLine().trim());

            // 创建删除服务请求
            DeleteServiceRequest deleteRequest = new DeleteServiceRequest();
            deleteRequest.setMessageType(ContentMessageType.deleteService);
            deleteRequest.setServiceId(new ServiceId(serviceId));

            // 创建服务配置请求
            ServiceConfigRequest configRequest = new ServiceConfigRequest();
            configRequest.setDeleteServiceRequest(deleteRequest);

            // 发送请求并处理响应
            // 创建消息帧
            MessageRequestFrame frame = new MessageRequestFrame();
            frame.setVersion(new Uint8(1));
            frame.setContent(new MessageRequestFrame.Content());
            frame.getContent().setServiceConfigRequest(configRequest);
            MessageResponseFrame responseFrame = sendRequest(frame);

            // 输出响应信息
            if (responseFrame.getContent().hasServiceConfigResponse()) {
                ServiceConfigResponse response = responseFrame.getContent().getServiceConfigResponse();
                System.out.println("服务删除成功，服务ID: " + response.getServiceId());
            } else if (responseFrame.getContent().hasError()) {
                ErrorResponse errorResponse = responseFrame.getContent().getError();
                System.out.println("请求失败: " + errorResponse.getErrorState());
            }
        } catch (Exception e) {
            System.err.println("删除服务失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 处理更新服务请求
     */
    private static void handleUpdateService() {
        try {
            // 获取要更新的服务ID
            System.out.print("请输入要更新的服务ID: ");
            int serviceId = Integer.parseInt(scanner.nextLine().trim());

            // 获取网络类型
            System.out.println("请选择网络类型:");
            System.out.println("0. 发送端 (SENDER)");
            System.out.println("1. 接收端 (RECEIVER)");
            System.out.print("请输入选择: ");
            int networkChoice = Integer.parseInt(scanner.nextLine().trim());
            Network network = (networkChoice == 0) ? Network.sender : Network.receiver;

            // 创建更新服务请求
            UpdateServiceRequest updateRequest = new UpdateServiceRequest();
            updateRequest.setMessageType(ContentMessageType.updateService);
            updateRequest.setServiceId(new ServiceId(serviceId));
            updateRequest.setNetwork(network);


            if (network == Network.sender) {
                // 发送端特有字段
                System.out.print("是否更新代理IP (y/n): ");
                if (scanner.nextLine().trim().equalsIgnoreCase("y")) {
                    System.out.print("请输入新的代理IP: ");
                    String proxyIp = scanner.nextLine().trim();
                    updateRequest.setProxyIp(IPUtil.createIPAddress(proxyIp));
                }

                System.out.print("是否更新代理端口 (y/n): ");
                if (scanner.nextLine().trim().equalsIgnoreCase("y")) {
                    System.out.print("请输入新的代理端口: ");
                    int proxyPort = Integer.parseInt(scanner.nextLine().trim());
                    updateRequest.setProxyPort(new PortNumber(proxyPort));
                }

                System.out.print("是否更新内容关键字检测位图 (y/n): ");
                if (scanner.nextLine().trim().equalsIgnoreCase("y")) {
                    System.out.print("请输入新的内容关键字检测位图值 (十六进制，例如 07): ");
                    String keyCheckHex = scanner.nextLine().trim();
                    byte[] contentKeyCheck = new byte[1];
                    contentKeyCheck[0] = (byte) Integer.parseInt(keyCheckHex, 16);
                    updateRequest.setContentKeyCheck(new ContentKeyCheck(contentKeyCheck));
                }

                System.out.print("是否更新协议过滤位图 (y/n): ");
                if (scanner.nextLine().trim().equalsIgnoreCase("y")) {
                    System.out.print("请输入新的协议过滤位图值 (十六进制，例如 05): ");
                    String protocolFilterHex = scanner.nextLine().trim();
                    byte[] protocolFilter = new byte[1];
                    protocolFilter[0] = (byte) Integer.parseInt(protocolFilterHex, 16);
                    updateRequest.setProtocolFilter(new ProtocolFilter(protocolFilter));
                }

                System.out.print("是否更新未通过处理方式 (y/n): ");
                if (scanner.nextLine().trim().equalsIgnoreCase("y")) {
                    System.out.println("请选择新的未通过处理方式:");
                    System.out.println("0. ALLOW");
                    System.out.println("1. DENY");
                    System.out.print("请输入选择: ");
                    int dealChoice = Integer.parseInt(scanner.nextLine().trim());
                    PermissionState unpassDeal;
                    switch (dealChoice) {
                        case 0:
                            unpassDeal = PermissionState.allow;
                            break;
                        case 1:
                            unpassDeal = PermissionState.forbidden;
                            break;
                        default:
                            unpassDeal = PermissionState.allow;
                    }
                    updateRequest.setUnpassDeal(unpassDeal);
                }
            } else {
                // 接收端特有字段
                System.out.print("是否更新服务器IP (y/n): ");
                if (scanner.nextLine().trim().equalsIgnoreCase("y")) {
                    System.out.print("请输入新的服务器IP: ");
                    String serverIp = scanner.nextLine().trim();
                    updateRequest.setServerIp(IPUtil.createIPAddress(serverIp));
                }

                System.out.print("是否更新服务器端口 (y/n): ");
                if (scanner.nextLine().trim().equalsIgnoreCase("y")) {
                    System.out.print("请输入新的服务器端口: ");
                    int serverPort = Integer.parseInt(scanner.nextLine().trim());
                    updateRequest.setServerPort(new PortNumber(serverPort));
                }
            }

            // 创建服务配置请求
            ServiceConfigRequest configRequest = new ServiceConfigRequest();
            configRequest.setUpdateServiceRequest(updateRequest);

            // 发送请求并处理响应
            // 创建消息帧
            MessageRequestFrame frame = new MessageRequestFrame();
            frame.setVersion(new Uint8(1));
            frame.setContent(new MessageRequestFrame.Content());
            frame.getContent().setServiceConfigRequest(configRequest);
            MessageResponseFrame responseFrame = sendRequest(frame);

            // 输出响应信息
            if (responseFrame.getContent().hasServiceConfigResponse()) {
                ServiceConfigResponse response = responseFrame.getContent().getServiceConfigResponse();
                System.out.println("服务更新成功，服务ID: " + response.getServiceId());
            } else if (responseFrame.getContent().hasError()) {
                ErrorResponse errorResponse = responseFrame.getContent().getError();
                System.out.println("请求失败: " + errorResponse.getErrorState());
            }
        } catch (Exception e) {
            System.err.println("更新服务失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 处理服务启停请求
     */
    private static void handleServiceControl() {
        try {
            // 获取服务ID
            System.out.print("请输入服务ID: ");
            int serviceId = Integer.parseInt(scanner.nextLine().trim());

            // 获取启停操作
            System.out.println("请选择操作:");
            System.out.println("0. 启动服务");
            System.out.println("1. 停止服务");
            System.out.print("请输入选择: ");
            int actionChoice = Integer.parseInt(scanner.nextLine().trim());
            ServiceControlAction action = (actionChoice == 0) ? ServiceControlAction.start : ServiceControlAction.stop;

            // 创建服务控制请求
            ServiceControlRequest controlRequest = new ServiceControlRequest();
            controlRequest.setMessageType(ContentMessageType.controlService);
            controlRequest.setServiceId(new ServiceId(serviceId));
            controlRequest.setServiceStartOrStop(action);

            // 发送请求并处理响应
            // 创建消息帧
            MessageRequestFrame frame = new MessageRequestFrame();
            frame.setVersion(new Uint8(1));
            frame.setContent(new MessageRequestFrame.Content());
            frame.getContent().setServiceControlRequest(controlRequest);
            MessageResponseFrame responseFrame = sendRequest(frame);

            // 输出响应信息
            if (responseFrame.getContent().hasServiceControlResponse()) {
                ServiceControlResponse response = responseFrame.getContent().getServiceControlResponse();
                System.out.println("服务" + (action == ServiceControlAction.start ? "启动" : "停止") + "请求成功");
                System.out.println("服务ID: " + response.getServiceId());
                System.out.println("操作: " + (response.getServiceStartOrStop() == ServiceControlAction.start ? "启动" : "停止"));
            } else if (responseFrame.getContent().hasError()) {
                ErrorResponse errorResponse = responseFrame.getContent().getError();
                System.out.println("请求失败: " + errorResponse.getErrorState());
            }
        } catch (Exception e) {
            System.err.println("服务控制请求失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 处理服务状态查询请求
     */
    private static void handleServiceStatusQuery() {
        try {
            // 获取服务ID
            System.out.print("请输入服务ID: ");
            String serviceId = scanner.nextLine().trim();

            // 创建服务状态查询请求
            ServiceStatusQueryRequest queryRequest = new ServiceStatusQueryRequest();
            queryRequest.setMessageType(ContentMessageType.queryWorkStatus);
            queryRequest.setServiceId(new ServiceId(Short.parseShort(serviceId)));

            // 发送请求并处理响应
            // 创建消息帧
            MessageRequestFrame frame = new MessageRequestFrame();
            frame.setVersion(new Uint8(1));
            frame.setContent(new MessageRequestFrame.Content());
            frame.getContent().setServiceStatusQueryRequest(queryRequest);
            MessageResponseFrame responseFrame = sendRequest(frame);

            // 输出响应信息
            if (responseFrame.getContent().hasServiceStatusQueryResponse()) {
                ServiceStatusQueryResponse response = responseFrame.getContent().getServiceStatusQueryResponse();
                System.out.println("服务状态查询成功");
                System.out.println("服务ID: " + response.getServiceId());

                ServiceStatus status = response.getServiceStatus();
                String statusDesc = "未知";
                if (status == ServiceStatus.running) {
                    statusDesc = "运行中";
                } else if (status == ServiceStatus.stopped) {
                    statusDesc = "已停止";
                }
                System.out.println("服务状态: " + statusDesc);
            } else if (responseFrame.getContent().hasError()) {
                ErrorResponse errorResponse = responseFrame.getContent().getError();
                System.out.println("请求失败: " + errorResponse.getErrorState());
            }
        } catch (Exception e) {
            System.err.println("服务状态查询失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 处理服务信息查询请求
     */
    private static void handleServiceInfoQuery() {
        try {
            // 获取服务ID
            System.out.print("请输入服务ID: ");
            String serviceId = scanner.nextLine().trim();

            // 获取网络类型
            System.out.println("请选择网络类型:");
            System.out.println("0. 发送端 (SENDER)");
            System.out.println("1. 接收端 (RECEIVER)");
            System.out.print("请输入选择: ");
            int networkChoice = Integer.parseInt(scanner.nextLine().trim());
            Network network = (networkChoice == 0) ? Network.sender : Network.receiver;

            // 创建服务信息查询请求
            ServiceConfigQueryRequest queryRequest = new ServiceConfigQueryRequest();
            queryRequest.setMessageType(ContentMessageType.queryServiceConfig);
            queryRequest.setServiceId(new ServiceId(Short.parseShort(serviceId)));
            queryRequest.setNetwork(network);

            // 发送请求并处理响应
            // 创建消息帧
            MessageRequestFrame frame = new MessageRequestFrame();
            frame.setVersion(new Uint8(1));
            frame.setContent(new MessageRequestFrame.Content());
            frame.getContent().setServiceConfigQueryRequest(queryRequest);
            MessageResponseFrame responseFrame = sendRequest(frame);

            // 输出响应信息
            if (responseFrame.getContent().hasServiceConfigQueryResponse()) {
                ServiceConfigQueryResponse response =  responseFrame.getContent().getServiceConfigQueryResponse();
                System.out.println("服务信息查询成功");
                System.out.println("服务ID: " + response.getServiceId());
                System.out.println("显示名称: " + new String(response.getDisplayname().byteArrayValue()));
                System.out.println("网络类型: " + (response.getNetwork() == Network.sender ? "发送端" : "接收端"));

                if (response.getNetwork() == Network.sender) {
                    if (response.getProxyIp() != null) {
                        System.out.println("代理IP: " + IPUtil.ipAddressToString(response.getProxyIp()));
                    }
                    if (response.getProxyPort().intValue() != 0) {
                        System.out.println("代理端口: " + response.getProxyPort());
                    }
                } else {
                    if (response.getServerIp() != null) {
                        System.out.println("服务器IP: " + IPUtil.ipAddressToString(response.getServerIp()));
                    }
                    if (response.getServerPort().intValue() != 0) {
                        System.out.println("服务器端口: " + response.getServerPort());
                    }
                }
                if (response.getContentKeyCheck() != null) {
                    byte[] contentKeyCheck = response.getContentKeyCheck().byteArrayValue();
                    if (contentKeyCheck.length > 0) {
                        // 检查第0位 - 机动车牌校验
                        boolean carcheck = (contentKeyCheck[0] & 0x01) != 0;
                        // 检查第1位 - 身份证校验
                        boolean idcheck = (contentKeyCheck[0] & 0x02) != 0;
                        System.out.println("机动车牌校验: " + carcheck);
                        System.out.println("身份证校验: " + idcheck);
                    }
                }
                if (response.getProtocolFilter() != null) {
                    byte[] protocolFilter = response.getProtocolFilter().byteArrayValue();
                    if (protocolFilter.length > 0) {
                        // 检查第0位 - CRC16格式过滤
                        boolean crcfilter = (protocolFilter[0] & 0x01) != 0;
                        // 检查第1位 - ASN格式过滤
                        boolean asnfilter = (protocolFilter[0] & 0x02) != 0;
                        System.out.println("CRC16格式过滤: " + crcfilter);
                        System.out.println("ASN格式过滤: " + asnfilter);
                    }
                }
                if (response.getUnpassDeal() != null) {
                    System.out.println("未通过处理方式: " + (response.getUnpassDeal() == PermissionState.allow ? "ALLOW" : "DENY"));
                }
            } else if (responseFrame.getContent().hasError()) {
                ErrorResponse errorResponse = responseFrame.getContent().getError();
                System.out.println("请求失败: " + errorResponse.getErrorState());
            }
        } catch (Exception e) {
            System.err.println("服务信息查询失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 处理主机状态查询请求
     */
    private static void handleHostStatusQuery() {
        try {
            // 创建主机状态查询请求
            WorkStatusRequest queryRequest = new WorkStatusRequest();
            queryRequest.setMessageType(ContentMessageType.queryWorkStatus);
            queryRequest.setServiceId(new ServiceId(0)); // 主机查询通常使用0作为ID

            // 发送请求并处理响应
            // 创建消息帧
            MessageRequestFrame frame = new MessageRequestFrame();
            frame.setVersion(new Uint8(1));
            frame.setContent(new MessageRequestFrame.Content());
            frame.getContent().setWorkStatusRequest(queryRequest);
            MessageResponseFrame responseFrame = sendRequest(frame);

            // 输出响应信息
            if (responseFrame.getContent().hasWorkStatusResponse()) {
                WorkStatusResponse response = responseFrame.getContent().getWorkStatusResponse();
                System.out.println("主机状态查询成功");
                System.out.println("今日发送包数: " + response.getSendPkgNumToday());
                System.out.println("今日发送数据大小: " + response.getSendPkgSizeToday() + " 字节");
                System.out.println("今日接收包数: " + response.getRecvPkgNumToday());
                System.out.println("今日接收数据大小: " + response.getRecvPkgSizeToday() + " 字节");
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
                String dateStr = sdf.format(new Date(response.getDevTime().bigIntegerValue().longValue()));
                System.out.println("设备时间: " + dateStr);
            } else if (responseFrame.getContent().hasError()) {
                ErrorResponse errorResponse = responseFrame.getContent().getError();
                System.out.println("请求失败: " + errorResponse.getErrorState());
            }
        } catch (Exception e) {
            System.err.println("主机状态查询失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 处理获取所有服务ID请求
     */
    private static void handleGetAllServiceIds() {
        try {
            // 创建获取所有服务ID请求
            GetAllServiceIdsRequest request = new GetAllServiceIdsRequest();
            request.setMessageType(ContentMessageType.getAllServiceIds);

            // 创建消息帧
            MessageRequestFrame frame = new MessageRequestFrame();
            frame.setVersion(new Uint8(1));
            frame.setContent(new MessageRequestFrame.Content());
            frame.getContent().setGetAllServiceIdsRequest(request);

            // 发送请求并处理响应
            MessageResponseFrame responseFrame = sendRequest(frame);

            // 输出响应信息
            if (responseFrame.getContent().hasGetAllServiceIdsResponse()) {
                GetAllServiceIdsResponse response = responseFrame.getContent().getGetAllServiceIdsResponse();
                System.out.println("获取所有服务ID成功");

                GetAllServiceIdsResponse.ServiceIds serviceIds = response.getServiceIds();
                if (serviceIds.size() > 0) {
                    System.out.println("服务ID列表:");
                    for (int i = 0; i < serviceIds.size(); i++) {
                        ServiceId serviceId = serviceIds.get(i);
                        System.out.println("  - " + serviceId.intValue());
                    }
                } else {
                    System.out.println("没有找到服务ID");
                }
            } else if (responseFrame.getContent().hasError()) {
                ErrorResponse errorResponse = responseFrame.getContent().getError();
                System.out.println("请求失败: " + errorResponse.getErrorState());
            }
        } catch (Exception e) {
            System.err.println("获取所有服务ID失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 处理检查通信状态请求
     */
    private static void handleCheckCommStatus() {
        try {
            // 获取服务ID
            System.out.print("请输入服务ID: ");
            String serviceId = scanner.nextLine().trim();

            // 选择网络类型
            System.out.println("请选择网络类型:");
            System.out.println("1. 发送端 (sender)");
            System.out.println("2. 接收端 (receiver)");
            System.out.print("请输入选择: ");
            int networkChoice = Integer.parseInt(scanner.nextLine().trim());
            Network network = (networkChoice == 2) ? Network.receiver : Network.sender;

            // 创建检查通信状态请求
            CheckCommStatusRequest request = new CheckCommStatusRequest();
            request.setMessageType(ContentMessageType.queryWorkStatus); // 使用queryWorkStatus代替checkCommStatusRequest
            request.setServiceId(new ServiceId(Short.parseShort(serviceId)));
            request.setNetwork(network);

            // 创建消息帧
            MessageRequestFrame frame = new MessageRequestFrame();
            frame.setVersion(new Uint8(1));
            frame.setContent(new MessageRequestFrame.Content());
            frame.getContent().setCheckCommStatusRequest(request);

            // 发送请求并处理响应
            MessageResponseFrame responseFrame = sendRequest(frame);

            // 输出响应信息
            if (responseFrame.getContent().hasCheckCommStatusResponse()) {
                CheckCommStatusResponse response = responseFrame.getContent().getCheckCommStatusResponse();
                System.out.println("检查通信状态成功");
                System.out.println("服务ID: " + response.getServiceId());
                System.out.println("网络类型: " + (response.getNetwork() == Network.sender ? "发送端" : "接收端"));
                System.out.println("连接状态: " + (response.getIsConnected() ? "已连接 (UP)" : "未连接 (DOWN)"));

                // 格式化时间戳
                long timestamp = response.getConnectionEventTime().bigIntegerValue().longValue();
                java.util.Date date = new java.util.Date(timestamp);
                java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
                System.out.println("连接事件时间: " + sdf.format(date));
            } else if (responseFrame.getContent().hasError()) {
                ErrorResponse errorResponse = responseFrame.getContent().getError();
                System.out.println("请求失败: " + errorResponse.getErrorState());
            }
        } catch (Exception e) {
            System.err.println("检查通信状态失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 处理发送包统计查询请求
     */
    private static void handleSendPacketStats() {
        try {
            // 获取服务ID
            System.out.print("请输入服务ID: ");
            String serviceId = scanner.nextLine().trim();

            // 选择网络类型
            System.out.println("请选择网络类型:");
            System.out.println("1. 发送端 (sender)");
            System.out.println("2. 接收端 (receiver)");
            System.out.print("请输入选择: ");
            int networkChoice = Integer.parseInt(scanner.nextLine().trim());
            Network network = (networkChoice == 2) ? Network.receiver : Network.sender;

            // 创建发送包统计请求
            SendPacketStatsRequest request = new SendPacketStatsRequest();
            request.setMessageType(ContentMessageType.sendPacketStats);
            request.setServiceId(new ServiceId(Short.parseShort(serviceId)));
            request.setNetwork(network);

            // 询问是否设置时间段
            System.out.print("是否设置查询时间段（分钟数）? (y/n): ");
            if (scanner.nextLine().trim().equalsIgnoreCase("y")) {
                System.out.print("请输入查询分钟数: ");
                int periodMinutes = Integer.parseInt(scanner.nextLine().trim());
                request.setPeriod(new Uint32(periodMinutes));
            }

            // 创建消息帧
            MessageRequestFrame frame = new MessageRequestFrame();
            frame.setVersion(new Uint8(1));
            frame.setContent(new MessageRequestFrame.Content());
            frame.getContent().setSendPacketStatsRequest(request);

            // 发送请求并处理响应
            MessageResponseFrame responseFrame = sendRequest(frame);

            // 输出响应信息
            if (responseFrame.getContent().hasSendPacketStatsResponse()) {
                SendPacketStatsResponse response = responseFrame.getContent().getSendPacketStatsResponse();
                System.out.println("发送包统计查询成功");
                System.out.println("服务ID: " + response.getServiceId());
                System.out.println("网络类型: " + (response.getNetwork() == Network.sender ? "发送端" : "接收端"));

                if (response.hasPeriod()) {
                    System.out.println("查询时间段: " + response.getPeriod() + " 分钟");
                }

                SendPacketStatsResponse.PacketStats packetStats = response.getPacketStats();
                if (packetStats.size() > 0) {
                    System.out.println("包统计数据:");
                    for (int i = 0; i < packetStats.size(); i++) {
                        PacketStats stats = packetStats.get(i);
                        String packetTypeName = getPacketTypeName(stats.getPacketType());
                        System.out.println("  - " + packetTypeName + ": " + stats.getPacketCount() + " 个");
                    }
                } else {
                    System.out.println("没有找到包统计数据");
                }
            } else if (responseFrame.getContent().hasError()) {
                ErrorResponse errorResponse = responseFrame.getContent().getError();
                System.out.println("请求失败: " + errorResponse.getErrorState());
            }
        } catch (Exception e) {
            System.err.println("发送包统计查询失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 处理接收包统计查询请求
     */
    private static void handleReceivePacketStats() {
        try {
            // 获取服务ID
            System.out.print("请输入服务ID: ");
            String serviceId = scanner.nextLine().trim();

            // 选择网络类型
            System.out.println("请选择网络类型:");
            System.out.println("1. 发送端 (sender)");
            System.out.println("2. 接收端 (receiver)");
            System.out.print("请输入选择: ");
            int networkChoice = Integer.parseInt(scanner.nextLine().trim());
            Network network = (networkChoice == 2) ? Network.receiver : Network.sender;

            // 创建接收包统计请求
            ReceivePacketStatsRequest request = new ReceivePacketStatsRequest();
            request.setMessageType(ContentMessageType.receivePacketStats);
            request.setServiceId(new ServiceId(Short.parseShort(serviceId)));
            request.setNetwork(network);

            // 询问是否设置时间段
            System.out.print("是否设置查询时间段（分钟数）? (y/n): ");
            if (scanner.nextLine().trim().equalsIgnoreCase("y")) {
                System.out.print("请输入查询分钟数: ");
                int periodMinutes = Integer.parseInt(scanner.nextLine().trim());
                request.setPeriod(new Uint32(periodMinutes));
            }

            // 创建消息帧
            MessageRequestFrame frame = new MessageRequestFrame();
            frame.setVersion(new Uint8(1));
            frame.setContent(new MessageRequestFrame.Content());
            frame.getContent().setReceivePacketStatsRequest(request);

            // 发送请求并处理响应
            MessageResponseFrame responseFrame = sendRequest(frame);

            // 输出响应信息
            if (responseFrame.getContent().hasReceivePacketStatsResponse()) {
                ReceivePacketStatsResponse response = responseFrame.getContent().getReceivePacketStatsResponse();
                System.out.println("接收包统计查询成功");
                System.out.println("服务ID: " + response.getServiceId());
                System.out.println("网络类型: " + (response.getNetwork() == Network.sender ? "发送端" : "接收端"));

                if (response.hasPeriod()) {
                    System.out.println("查询时间段: " + response.getPeriod() + " 分钟");
                }

                ReceivePacketStatsResponse.PacketStats packetStats = response.getPacketStats();
                if (packetStats.size() > 0) {
                    System.out.println("包统计数据:");
                    for (int i = 0; i < packetStats.size(); i++) {
                        PacketStats stats = packetStats.get(i);
                        String packetTypeName = getPacketTypeName(stats.getPacketType());
                        System.out.println("  - " + packetTypeName + ": " + stats.getPacketCount() + " 个");
                    }
                } else {
                    System.out.println("没有找到包统计数据");
                }
            } else if (responseFrame.getContent().hasError()) {
                ErrorResponse errorResponse = responseFrame.getContent().getError();
                System.out.println("请求失败: " + errorResponse.getErrorState());
            }
        } catch (Exception e) {
            System.err.println("接收包统计查询失败: " + e.getMessage());
            e.printStackTrace();
        }
    }


    /**
     * 处理设置网口IP请求
     */
    private static void handleSetInterfaceIp() {
        try {
            // 获取网口类型
            System.out.println("请选择网口类型:");
            System.out.println("0. 管理口 (management) - eth0");
            System.out.println("1. 业务口 (business) - eth1");
            System.out.print("请输入选择: ");
            int interfaceChoice = Integer.parseInt(scanner.nextLine().trim());
            InterfaceType interfaceType = (interfaceChoice == 0) ? InterfaceType.management : InterfaceType.business;

            // 获取IP地址
            System.out.print("请输入IP地址: ");
            String ipAddress = scanner.nextLine().trim();

            // 获取子网掩码
            System.out.print("请输入子网掩码: ");
            String subnetMask = scanner.nextLine().trim();

            // 创建设置网口IP请求
            SetInterfaceIpRequest request = new SetInterfaceIpRequest();
            request.setMessageType(ContentMessageType.setInterfaceIpService);
            request.setInterfaceType(interfaceType);
            request.setIpAddress(IPUtil.createIPAddress(ipAddress));
            request.setSubnetMask(IPUtil.createIPAddress(subnetMask));

            // 可选：获取网关（注意：只能有一个网卡设置网关）
            System.out.print("是否设置网关 (y/n) [注意：只能有一个网卡设置网关]: ");
            if (scanner.nextLine().trim().equalsIgnoreCase("y")) {
                System.out.print("请输入网关地址: ");
                String gateway = scanner.nextLine().trim();
                request.setGateway(IPUtil.createIPAddress(gateway));
            }

            // 可选：获取路由
            System.out.print("是否设置路由 (y/n): ");
            if (scanner.nextLine().trim().equalsIgnoreCase("y")) {
                System.out.print("请输入路由地址: ");
                String route = scanner.nextLine().trim();
                request.setIproute(IPUtil.createIPAddress(route));
            }

            // 创建消息帧
            MessageRequestFrame frame = new MessageRequestFrame();
            frame.setVersion(new Uint8(1));
            frame.setContent(new MessageRequestFrame.Content());
            frame.getContent().setSetInterfaceIpRequest(request);

            // 发送请求并处理响应
            MessageResponseFrame responseFrame = sendRequest(frame);

            // 输出响应信息
            if (responseFrame.getContent().hasSetInterfaceIpResponse()) {
                SetInterfaceIpResponse response = responseFrame.getContent().getSetInterfaceIpResponse();
                System.out.println("设置网口IP成功");
                System.out.println("网口类型: " + (response.getInterfaceType() == InterfaceType.management ? "管理口" : "业务口"));
                System.out.println("当前IP: " + IPUtil.ipAddressToString(response.getCurrentIpAddress()));
                System.out.println("当前子网掩码: " + IPUtil.ipAddressToString(response.getCurrentSubnetMask()));

                if (response.hasCurrentGateway()) {
                    System.out.println("当前网关: " + IPUtil.ipAddressToString(response.getCurrentGateway()));
                }

                if (response.hasCurIproute()) {
                    System.out.println("当前路由: " + IPUtil.ipAddressToString(response.getCurIproute()));
                }

                System.out.println("执行结果: " + (response.getResult() == 0 ? "成功" : "失败"));
            } else if (responseFrame.getContent().hasError()) {
                ErrorResponse errorResponse = responseFrame.getContent().getError();
                System.out.println("请求失败: " + errorResponse.getErrorState());
            }
        } catch (Exception e) {
            System.err.println("设置网口IP失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 处理查询上位机管理请求
     */
    private static void handleQueryHostMng() {
        try {
            // 创建查询上位机管理请求
            QueryHostMngRequest request = new QueryHostMngRequest();
            request.setMessageType(ContentMessageType.hostMngService);

            // 创建消息帧
            MessageRequestFrame frame = new MessageRequestFrame();
            frame.setVersion(new Uint8(1));
            frame.setContent(new MessageRequestFrame.Content());
            frame.getContent().setQueryHostMngRequest(request);

            // 发送请求并处理响应
            MessageResponseFrame responseFrame = sendRequest(frame);

            // 输出响应信息
            if (responseFrame.getContent().hasQueryHostMngResponse()) {
                QueryHostMngResponse response = responseFrame.getContent().getQueryHostMngResponse();
                System.out.println("查询上位机管理配置成功");
                System.out.println("上位机IP: " + IPUtil.ipAddressToString(response.getCenterIP()));
                System.out.println("认证端口: " + response.getAuthPort());
                System.out.println("告警端口: " + response.getAlarmPort());
                System.out.println("证书管理端口: " + response.getCertMngPort());
                System.out.println("业务管理端口: " + response.getSgPort());
            } else if (responseFrame.getContent().hasError()) {
                ErrorResponse errorResponse = responseFrame.getContent().getError();
                System.out.println("请求失败: " + errorResponse.getErrorState());
            }
        } catch (Exception e) {
            System.err.println("查询上位机管理配置失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 处理设置上位机管理请求
     */
    private static void handleSetHostMng() {
        try {
            // 创建设置上位机管理请求
            SetHostMngRequest request = new SetHostMngRequest();
            request.setMessageType(ContentMessageType.hostMngService);

            // 获取上位机IP
            System.out.print("是否设置上位机IP (y/n): ");
            if (scanner.nextLine().trim().equalsIgnoreCase("y")) {
                System.out.print("请输入上位机IP: ");
                String centerIP = scanner.nextLine().trim();
                request.setCenterIP(IPUtil.createIPAddress(centerIP));
            }

            // 获取认证端口
            System.out.print("是否设置认证端口 (y/n): ");
            if (scanner.nextLine().trim().equalsIgnoreCase("y")) {
                System.out.print("请输入认证端口: ");
                int authPort = Integer.parseInt(scanner.nextLine().trim());
                request.setAuthPort(new PortNumber(authPort));
            }

            // 获取告警端口
            System.out.print("是否设置告警端口 (y/n): ");
            if (scanner.nextLine().trim().equalsIgnoreCase("y")) {
                System.out.print("请输入告警端口: ");
                int alarmPort = Integer.parseInt(scanner.nextLine().trim());
                request.setAlarmPort(new PortNumber(alarmPort));
            }

            // 获取证书管理端口
            System.out.print("是否设置证书管理端口 (y/n): ");
            if (scanner.nextLine().trim().equalsIgnoreCase("y")) {
                System.out.print("请输入证书管理端口: ");
                int certMngPort = Integer.parseInt(scanner.nextLine().trim());
                request.setCertMngPort(new PortNumber(certMngPort));
            }

            // 获取业务管理端口
            System.out.print("是否设置业务管理端口 (y/n): ");
            if (scanner.nextLine().trim().equalsIgnoreCase("y")) {
                System.out.print("请输入业务管理端口: ");
                int sgPort = Integer.parseInt(scanner.nextLine().trim());
                request.setSgPort(new PortNumber(sgPort));
            }

            // 创建消息帧
            MessageRequestFrame frame = new MessageRequestFrame();
            frame.setVersion(new Uint8(1));
            frame.setContent(new MessageRequestFrame.Content());
            frame.getContent().setSetHostMngRequest(request);

            // 发送请求并处理响应
            MessageResponseFrame responseFrame = sendRequest(frame);

            // 输出响应信息
            if (responseFrame.getContent().hasSetHostMngResponse()) {
                SetHostMngResponse response = responseFrame.getContent().getSetHostMngResponse();
                System.out.println("设置上位机管理配置成功");
                System.out.println("上位机IP: " + IPUtil.ipAddressToString(response.getCenterIP()));
                System.out.println("认证端口: " + response.getAuthPort());
                System.out.println("告警端口: " + response.getAlarmPort());
                System.out.println("证书管理端口: " + response.getCertMngPort());
                System.out.println("业务管理端口: " + response.getSgPort());
                System.out.println("执行结果: " + (response.getResult() == 0 ? "成功" : "失败"));

                if (response.getResult() == 0) {
                    System.out.println("\n注意: 配置已更新，服务器将在1秒后重启以应用新配置。");
                }
            } else if (responseFrame.getContent().hasError()) {
                ErrorResponse errorResponse = responseFrame.getContent().getError();
                System.out.println("请求失败: " + errorResponse.getErrorState());
            }
        } catch (Exception e) {
            System.err.println("设置上位机管理配置失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 处理查询源设备配置请求
     */
    private static void handleQuerySourceDevice() {
        try {
            // 获取服务ID
            System.out.print("请输入服务ID: ");
            int serviceId = Integer.parseInt(scanner.nextLine().trim());

            // 创建查询源设备请求
            QuerySourceDeviceRequest request = new QuerySourceDeviceRequest();
            request.setMessageType(ContentMessageType.sourceDeviceService);
            request.setServiceId(new ServiceId(serviceId));

            // 创建消息帧
            MessageRequestFrame frame = new MessageRequestFrame();
            frame.setVersion(new Uint8(1));
            frame.setContent(new MessageRequestFrame.Content());
            frame.getContent().setQuerySourceDeviceRequest(request);

            // 发送请求并处理响应
            MessageResponseFrame responseFrame = sendRequest(frame);

            // 输出响应信息
            if (responseFrame.getContent().hasQuerySourceDeviceResponse()) {
                QuerySourceDeviceResponse response = responseFrame.getContent().getQuerySourceDeviceResponse();
                System.out.println("查询源设备配置成功");
                System.out.println("服务ID: " + response.getServiceId());

                QuerySourceDeviceResponse.CurrentDevices currentDevices = response.getCurrentDevices();
                if (currentDevices != null && currentDevices.size() > 0) {
                    System.out.println("当前源设备列表:");
                    for (int i = 0; i < currentDevices.size(); i++) {
                        SourceDevice device = currentDevices.get(i);
                        String deviceIP = IPUtil.ipAddressToString(device.getIpAddress());
                        String devicePort = device.hasPort() ? String.valueOf(device.getPort()) : "未设置";
                        System.out.println("  设备 " + (i + 1) + ": IP=" + deviceIP + ", Port=" + devicePort);
                    }
                } else {
                    System.out.println("当前没有配置源设备");
                }
            } else if (responseFrame.getContent().hasError()) {
                ErrorResponse errorResponse = responseFrame.getContent().getError();
                System.out.println("请求失败: " + errorResponse.getErrorState());
            }
        } catch (Exception e) {
            System.err.println("查询源设备配置失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 处理设置源设备配置请求
     */
    private static void handleSetSourceDevice() {
        try {
            // 获取服务ID
            System.out.print("请输入服务ID: ");
            int serviceId = Integer.parseInt(scanner.nextLine().trim());

            // 创建设置源设备请求
            SetSourceDeviceRequest request = new SetSourceDeviceRequest();
            request.setMessageType(ContentMessageType.sourceDeviceService);
            request.setServiceId(new ServiceId(serviceId));

            // 创建源设备列表
            SetSourceDeviceRequest.Sourcedevices sourcedevices = new SetSourceDeviceRequest.Sourcedevices();

            // 获取源设备数量
            System.out.print("请输入要配置的源设备数量: ");
            int deviceCount = Integer.parseInt(scanner.nextLine().trim());

            for (int i = 0; i < deviceCount; i++) {
                System.out.println("\n配置第 " + (i + 1) + " 个源设备:");

                // 获取设备IP
                System.out.print("请输入设备IP: ");
                String deviceIP = scanner.nextLine().trim();

                // 创建源设备对象
                SourceDevice device = new SourceDevice();
                device.setIpAddress(IPUtil.createIPAddress(deviceIP));

                // 可选：获取设备端口
                System.out.print("是否设置设备端口 (y/n): ");
                if (scanner.nextLine().trim().equalsIgnoreCase("y")) {
                    System.out.print("请输入设备端口: ");
                    int devicePort = Integer.parseInt(scanner.nextLine().trim());
                    device.setPort(devicePort);
                }

                // 添加到列表
                sourcedevices.add(device);
            }

            request.setSourcedevices(sourcedevices);

            // 创建消息帧
            MessageRequestFrame frame = new MessageRequestFrame();
            frame.setVersion(new Uint8(1));
            frame.setContent(new MessageRequestFrame.Content());
            frame.getContent().setSetSourceDeviceRequest(request);

            // 发送请求并处理响应
            MessageResponseFrame responseFrame = sendRequest(frame);

            // 输出响应信息
            if (responseFrame.getContent().hasSetSourceDeviceResponse()) {
                SetSourceDeviceResponse response = responseFrame.getContent().getSetSourceDeviceResponse();
                System.out.println("设置源设备配置请求处理完成");
                System.out.println("服务ID: " + response.getServiceId());

                if (response.getResult() == 0) {
                    System.out.println("执行结果: 成功");
                } else {
                    System.out.println("执行结果: 失败");
                    System.out.println("可能原因: 服务正在运行中，无法修改源设备配置");
                }

                SetSourceDeviceResponse.CurrentDevices currentDevices = response.getCurrentDevices();
                if (currentDevices != null && currentDevices.size() > 0) {
                    System.out.println("当前源设备列表:");
                    for (int i = 0; i < currentDevices.size(); i++) {
                        SourceDevice device = currentDevices.get(i);
                        String deviceIP = IPUtil.ipAddressToString(device.getIpAddress());
                        String devicePort = device.hasPort() ? String.valueOf(device.getPort()) : "未设置";
                        System.out.println("  设备 " + (i + 1) + ": IP=" + deviceIP + ", Port=" + devicePort);
                    }
                } else {
                    System.out.println("当前没有配置源设备");
                }

                if (response.getResult() != 0) {
                    System.out.println("\n提示: 如需修改源设备配置，请先停止服务，修改完成后再启动服务。");
                }
            } else if (responseFrame.getContent().hasError()) {
                ErrorResponse errorResponse = responseFrame.getContent().getError();
                System.out.println("请求失败: " + errorResponse.getErrorState());
            }
        } catch (Exception e) {
            System.err.println("设置源设备配置失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 获取包类型名称
     */
    private static String getPacketTypeName(PacketType packetType) {
        if (packetType == PacketType.cim) {
            return "CIM";
        } else if (packetType == PacketType.slgs) {
            return "SLGS";
        } else if (packetType == PacketType.lcs) {
            return "LCS";
        } else if (packetType == PacketType.bsm) {
            return "BSM";
        } else if (packetType == PacketType.other) {
            return "其他";
        } else {
            return "未知";
        }
    }

    /**
     * 发送请求并返回响应
     */
    private static MessageResponseFrame sendRequest(MessageRequestFrame request) throws IOException {

        // 编码消息
        byte[] encodedData = OssServAdapter.encodeRequest(request);

        System.out.println("正在发送请求...");

        // 发送请求
        byte[] response = client.post("/sg", encodedData);

        // 解码响应
        MessageResponseFrame responseFrame = OssServAdapter.decodeResponse(response);
        System.out.println("请求发送成功!");
        System.out.println("响应版本: " + responseFrame.getVersion());
        System.out.println("响应大小: " + response.length + " 字节");

        return responseFrame;
    }
}
