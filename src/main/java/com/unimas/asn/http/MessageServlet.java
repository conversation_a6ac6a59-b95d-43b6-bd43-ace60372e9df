package com.unimas.asn.http;

import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.math.BigInteger;
import java.time.LocalDateTime;
import java.time.ZoneId;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.unimas.asn.bean.UdpConfig;
import com.unimas.asn.codec.OssServAdapter;
import com.unimas.asn.logic.ServiceConfigLogic;
import com.unimas.asn.logic.ServiceConfigQueryLogic;
import com.unimas.asn.logic.ServiceStatusLogic;
import com.unimas.asn.db.UdpAuditDAO;

import com.unimas.asn.servicemanager.servicemanagementhttp.*;
import com.unimas.asn.util.*;
import com.unimassystem.main.License;
import com.oss.asn1.BOOLEAN;
import com.oss.asn1.UTF8String16;
import org.bouncycastle.util.encoders.Hex;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;



/**
 * 处理ASN.1消息的HTTP Servlet
 */
public class MessageServlet extends HttpServlet {
    private static final Logger logger = LoggerFactory.getLogger(MessageServlet.class);

    // 授权模块名称
    private static final String SG_BASE_MODEL = "baseModel";

    /**
     * 检查授权是否有效
     * @return 授权是否有效
     */
    private boolean isLicenseValid() {
        boolean ret = DeviceConfigReader.getInstance().getNetwork().equals(Network.sender);
        if(ret ){ //发送验证
            try {
                ret = new License().isValidTime(SG_BASE_MODEL);
            } catch (Exception e) {
                logger.error("检查授权时发生错误", e);
                ret =  false;
            }
        }else{ //接收不验证
            ret = true;
        }
        return ret;
    }

    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        // 检查授权是否有效
        if (!isLicenseValid()) {
            logger.error("授权已过期，拒绝处理请求");
            sendHttpErrorResponse(response, HttpServletResponse.SC_UNAUTHORIZED, "授权已过期，请联系管理员");
            return;
        }
        try {
            // 读取请求体
            byte[] requestBody = readRequestBody(request);
            logger.info("request body is {}", Hex.toHexString(requestBody));
            // 解码请求
            MessageRequestFrame frame = OssServAdapter.decode(requestBody, MessageRequestFrame.class);
            logger.info("decode message frame is {}", frame);
            // 处理消息
            byte[] responseData = processMessage(frame);
            logger.info("response data is {}", Hex.toHexString(responseData));
            // 发送响应
            response.setContentType("application/octet-stream");
            response.setContentLength(responseData.length);
            try (OutputStream os = response.getOutputStream()) {
                os.write(responseData);
                os.flush();
            }
        } catch (Exception e) {
            logger.error("Error processing request", e);
//            response.sendError(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            sendHttpErrorResponse(response, HttpServletResponse.SC_INTERNAL_SERVER_ERROR, "服务器内部错误");
        }
    }

    private byte[] readRequestBody(HttpServletRequest request) throws IOException {
        int contentLength = request.getContentLength();
        if (contentLength <= 0) {
            throw new IOException("Invalid content length: " + contentLength);
        }

        byte[] buffer = new byte[contentLength];
        try (InputStream is = request.getInputStream()) {
            int totalBytesRead = 0;
            while (totalBytesRead < contentLength) {
                int bytesRead = is.read(buffer, totalBytesRead, contentLength - totalBytesRead);
                if (bytesRead == -1) {
                    break;
                }
                totalBytesRead += bytesRead;
            }
            if (totalBytesRead != contentLength) {
                throw new IOException("Expected " + contentLength + " bytes but read " + totalBytesRead + " bytes");
            }
            return buffer;
        }
    }

    private byte[] processMessage(MessageRequestFrame frame) throws IOException {
        // 再次检查授权是否有效，防止doPost方法被绕过
        if (!isLicenseValid()) {
            logger.error("授权已过期，拒绝处理消息");
            throw new IOException("授权已过期");
        }

        try {
            // 验证请求帧
            RequestValidator.validateRequestFrame(frame);

            MessageRequestFrame.Content content = frame.getContent();
            // 创建响应帧
            MessageResponseFrame responseFrame = new MessageResponseFrame();
            responseFrame.setVersion(frame.getVersion());
            responseFrame.setContent(new MessageResponseFrame.Content());

            if (content.hasServiceConfigRequest()) {
                try {
                    ServiceConfigRequest request = content.getServiceConfigRequest();
                    // 验证服务配置请求
                    RequestValidator.validateServiceConfigRequest(request);
                    ServiceConfigResponse serviceConfigResponse = processServiceConfigRequest(request);
                    responseFrame.getContent().setServiceConfigResponse(serviceConfigResponse);
                } catch (ValidationException e) {
                    // 创建验证错误响应
                    ErrorResponse errorResponse = createErrorResponse(e.getErrorMessageContentType(), e.getErrorCode());
                    responseFrame.getContent().setError(errorResponse);
                    logger.error("服务配置请求验证失败: {}", e.getMessage());
                }
            } else if (content.hasServiceControlRequest()) {
                try {
                    ServiceControlRequest request = content.getServiceControlRequest();
                    // 验证服务控制请求
                    RequestValidator.validateServiceControlRequest(request);
                    ServiceControlResponse serviceControlResponse = processServiceControlRequest(request);
                    responseFrame.getContent().setServiceControlResponse(serviceControlResponse);
                } catch (ValidationException e) {
                    ErrorResponse errorResponse = createErrorResponse(e.getErrorMessageContentType(), e.getErrorCode());
                    responseFrame.getContent().setError(errorResponse);
                    logger.error("服务控制请求验证失败: {}", e.getMessage());
                }
            } else if (content.hasServiceStatusQueryRequest()) {
                try {
                    ServiceStatusQueryRequest request = content.getServiceStatusQueryRequest();
                    // 验证服务状态查询请求
                    RequestValidator.validateServiceStatusQueryRequest(request);
                    ServiceStatusQueryResponse serviceStatusQueryResponse = processServiceStatusQueryRequest(request);
                    responseFrame.getContent().setServiceStatusQueryResponse(serviceStatusQueryResponse);
                } catch (ValidationException e) {
                    ErrorResponse errorResponse = createErrorResponse(e.getErrorMessageContentType(), e.getErrorCode());
                    responseFrame.getContent().setError(errorResponse);
                    logger.error("服务状态查询请求验证失败: {}", e.getMessage());
                }
            } else if (content.hasServiceConfigQueryRequest()) {
                try {
                    ServiceConfigQueryRequest request = content.getServiceConfigQueryRequest();
                    // 验证服务配置查询请求
                    RequestValidator.validateServiceConfigQueryRequest(request);
                    ServiceConfigQueryResponse serviceConfigQueryResponse = processServiceConfigQueryRequest(request);
                    responseFrame.getContent().setServiceConfigQueryResponse(serviceConfigQueryResponse);
                } catch (ValidationException e) {
                    ErrorResponse errorResponse = createErrorResponse(e.getErrorMessageContentType(), e.getErrorCode());
                    responseFrame.getContent().setError(errorResponse);
                    logger.error("服务配置查询请求验证失败: {}", e.getMessage());
                }
            } else if (content.hasAlarmReportRequest()) {
                AlarmReportRequest request = content.getAlarmReportRequest();
                // 验证报警报告请求
//                    RequestValidator.validateAlarmReportRequest(request);
                AlarmReportResponse alarmReportResponse = processAlarmReportRequest(request);
                responseFrame.getContent().setAlarmReportResponse(alarmReportResponse);
            } else if (content.hasWorkStatusRequest()) {
                try {
                    WorkStatusRequest request = content.getWorkStatusRequest();
                    // 验证工作状态请求
                    RequestValidator.validateWorkStatusRequest(request);
                    WorkStatusResponse workStatusResponse = processWorkStatusRequest(request);
                    responseFrame.getContent().setWorkStatusResponse(workStatusResponse);
                } catch (ValidationException e) {
                    ErrorResponse errorResponse = createErrorResponse(e.getErrorMessageContentType(), e.getErrorCode());
                    responseFrame.getContent().setError(errorResponse);
                    logger.error("工作状态请求验证失败: {}", e.getMessage());
                }
            } else if(content.hasGetAllServiceIdsRequest()){
                GetAllServiceIdsRequest request = content.getGetAllServiceIdsRequest();
                GetAllServiceIdsResponse getAllServiceIdsResponse = processGetAllServiceIdsRequest(request);
                responseFrame.getContent().setGetAllServiceIdsResponse(getAllServiceIdsResponse);
            } else if(content.hasSendPacketStatsRequest()){
                try {
                    SendPacketStatsRequest request = content.getSendPacketStatsRequest();
                    // 验证发送包统计请求
                    RequestValidator.validateSendPacketStatsRequest(request);
                    SendPacketStatsResponse sendPacketStatsResponse = processSendPacketStatsRequest(request);
                    responseFrame.getContent().setSendPacketStatsResponse(sendPacketStatsResponse);
                } catch (ValidationException e) {
                    ErrorResponse errorResponse = createErrorResponse(e.getErrorMessageContentType(), e.getErrorCode());
                    responseFrame.getContent().setError(errorResponse);
                    logger.error("发送包统计请求验证失败: {}", e.getMessage());
                }
            } else if(content.hasReceivePacketStatsRequest()){
                try {
                    ReceivePacketStatsRequest request = content.getReceivePacketStatsRequest();
                    // 验证接收包统计请求
                    RequestValidator.validateReceivePacketStatsRequest(request);
                    ReceivePacketStatsResponse receivePacketStatsResponse = processReceivePacketStatsRequest(request);
                    responseFrame.getContent().setReceivePacketStatsResponse(receivePacketStatsResponse);
                } catch (ValidationException e) {
                    ErrorResponse errorResponse = createErrorResponse(e.getErrorMessageContentType(), e.getErrorCode());
                    responseFrame.getContent().setError(errorResponse);
                    logger.error("接收包统计请求验证失败: {}", e.getMessage());
                }
            } else if(content.hasCheckCommStatusRequest()){

                CheckCommStatusRequest checkCommStatusRequest = content.getCheckCommStatusRequest();
                CheckCommStatusResponse checkCommStatusResponse = processCheckCommStatusRequest(checkCommStatusRequest);
                responseFrame.getContent().setCheckCommStatusResponse(checkCommStatusResponse);
            } else if(content.hasSetInterfaceIpRequest()){
                try {
                    SetInterfaceIpRequest request = content.getSetInterfaceIpRequest();
                    // 验证设置网口IP请求
                    RequestValidator.validateSetInterfaceIpRequest(request);
                    SetInterfaceIpResponse setInterfaceIpResponse = processSetInterfaceIpRequest(request);
                    responseFrame.getContent().setSetInterfaceIpResponse(setInterfaceIpResponse);
                } catch (ValidationException e) {
                    ErrorResponse errorResponse = createErrorResponse(e.getErrorMessageContentType(), e.getErrorCode());
                    responseFrame.getContent().setError(errorResponse);
                    logger.error("设置网口IP请求验证失败: {}", e.getMessage());
                }
            } else if(content.hasQueryHostMngRequest()){
                try {
                    QueryHostMngRequest request = content.getQueryHostMngRequest();
                    // 验证查询上位机管理请求
                    RequestValidator.validateQueryHostMngRequest(request);
                    QueryHostMngResponse queryHostMngResponse = processQueryHostMngRequest(request);
                    responseFrame.getContent().setQueryHostMngResponse(queryHostMngResponse);
                } catch (ValidationException e) {
                    ErrorResponse errorResponse = createErrorResponse(e.getErrorMessageContentType(), e.getErrorCode());
                    responseFrame.getContent().setError(errorResponse);
                    logger.error("查询上位机管理请求验证失败: {}", e.getMessage());
                }
            } else if(content.hasSetHostMngRequest()){
                try {
                    SetHostMngRequest request = content.getSetHostMngRequest();
                    // 验证设置上位机管理请求
                    RequestValidator.validateSetHostMngRequest(request);
                    SetHostMngResponse setHostMngResponse = processSetHostMngRequest(request);
                    responseFrame.getContent().setSetHostMngResponse(setHostMngResponse);
                } catch (ValidationException e) {
                    ErrorResponse errorResponse = createErrorResponse(e.getErrorMessageContentType(), e.getErrorCode());
                    responseFrame.getContent().setError(errorResponse);
                    logger.error("设置上位机管理请求验证失败: {}", e.getMessage());
                }
            } else if(content.hasQuerySourceDeviceRequest()){
                try {
                    QuerySourceDeviceRequest request = content.getQuerySourceDeviceRequest();
                    // 验证查询源设备请求
                    RequestValidator.validateQuerySourceDeviceRequest(request);
                    QuerySourceDeviceResponse querySourceDeviceResponse = processQuerySourceDeviceRequest(request);
                    responseFrame.getContent().setQuerySourceDeviceResponse(querySourceDeviceResponse);
                } catch (ValidationException e) {
                    ErrorResponse errorResponse = createErrorResponse(e.getErrorMessageContentType(), e.getErrorCode());
                    responseFrame.getContent().setError(errorResponse);
                    logger.error("查询源设备请求验证失败: {}", e.getMessage());
                }
            } else if(content.hasSetSourceDeviceRequest()){
                try {
                    SetSourceDeviceRequest request = content.getSetSourceDeviceRequest();
                    // 验证设置源设备请求
                    RequestValidator.validateSetSourceDeviceRequest(request);
                    SetSourceDeviceResponse setSourceDeviceResponse = processSetSourceDeviceRequest(request);
                    responseFrame.getContent().setSetSourceDeviceResponse(setSourceDeviceResponse);
                } catch (ValidationException e) {
                    ErrorResponse errorResponse = createErrorResponse(e.getErrorMessageContentType(), e.getErrorCode());
                    responseFrame.getContent().setError(errorResponse);
                    logger.error("设置源设备请求验证失败: {}", e.getMessage());
                }
            }else {
                // 未知的请求类型
                ErrorResponse errorResponse = createErrorResponse( null, ProcessErrorState.messageStructureError);
                responseFrame.getContent().setError(errorResponse);
                logger.error("收到未知的请求类型");
            }

            logger.info("response frame is {}", responseFrame);
            // 编码响应
            return OssServAdapter.encode(responseFrame);
        } catch (ValidationException e) {
            // 处理顶层验证异常
            MessageResponseFrame errorFrame = createErrorResponseFrame(frame.getVersion().intValue(), e);
            logger.error("请求帧验证失败: {}", e.getMessage());
            return OssServAdapter.encode(errorFrame);
        }
    }

    /**
     * 创建带有错误信息的响应帧
     */
    private MessageResponseFrame createErrorResponseFrame(int version, ValidationException e) {
        MessageResponseFrame responseFrame = new MessageResponseFrame();
        responseFrame.setVersion(new Uint8(version));
        responseFrame.setContent(new MessageResponseFrame.Content());
        ErrorResponse errorResponse = createErrorResponse(e.getErrorMessageContentType(), e.getErrorCode());
        responseFrame.getContent().setError(errorResponse);
        return responseFrame;
    }

    /**
     * 创建错误响应对象
     */
    private ErrorResponse createErrorResponse(ContentMessageType messageType, ProcessErrorState code) {
        ErrorResponse error = new ErrorResponse();
        error.setErrorState(code);
        error.setMessageType(messageType);
        return error;
    }

    private ServiceConfigResponse processServiceConfigRequest(ServiceConfigRequest request) throws ValidationException {
        logger.info("Processing ServiceConfigRequest");
        ServiceConfigLogic serviceConfigLogic = new ServiceConfigLogic();
        return serviceConfigLogic.serviceConfigRequest(request);
    }

    private ServiceControlResponse processServiceControlRequest(ServiceControlRequest request) throws ValidationException {
        logger.info("Processing ServiceControlRequest for service: {}, action: {}",
            request.getServiceId(), request.getServiceStartOrStop());
        ServiceStatusLogic serviceStatusLogic = new ServiceStatusLogic();
        return serviceStatusLogic.startOrStop(request);
    }

    private ServiceStatusQueryResponse processServiceStatusQueryRequest(ServiceStatusQueryRequest request) {
        logger.info("Processing ServiceStatusQueryRequest for service: {}", request.getServiceId());
        ConfigXmlOperator configXmlOperator = new ConfigXmlOperator(Constant.CONFIG_PATH);
        int runFlag = 0;
        try {
            runFlag = configXmlOperator.getIsRun(request.getServiceId().intValue()+"")?0:1;
        } catch (Exception e) {
            logger.error("Error processing ServiceStatusQueryRequest", e);
            runFlag = 2;
        }
        ServiceStatusQueryResponse response = new ServiceStatusQueryResponse();
        response.setMessageType(ContentMessageType.queryServiceStatus);
        response.setServiceId(request.getServiceId());
        response.setServiceStatus(ServiceStatus.valueOf(runFlag)); // 示例状态
        return response;
    }

    private ServiceConfigQueryResponse processServiceConfigQueryRequest(ServiceConfigQueryRequest request) {
        logger.info("Processing ServiceConfigQueryRequest for service: {}, network: {}",
            request.getServiceId(), request.getNetwork());
        return new ServiceConfigQueryLogic().serviceConfigQuery(request);
    }

    private AlarmReportResponse processAlarmReportRequest(AlarmReportRequest request) {
        logger.info("Processing AlarmReportRequest for service: {}, type: {}",
            request.getServiceId(), request.getAlarmType());
        logger.info("alarm info = " + request);
        AlarmReportResponse response = new AlarmReportResponse();
        response.setMessageType(ContentMessageType.reportAlarm);
        response.setServiceId(request.getServiceId());
        return response;
    }

    private GetAllServiceIdsResponse processGetAllServiceIdsRequest(GetAllServiceIdsRequest request) {
        logger.info("Processing GetAllServiceIdsRequest");

        // 创建响应对象
        GetAllServiceIdsResponse response = new GetAllServiceIdsResponse();
        response.setMessageType(ContentMessageType.getAllServiceIds);

        // 创建服务ID集合
        GetAllServiceIdsResponse.ServiceIds serviceIds = new GetAllServiceIdsResponse.ServiceIds();

        try {
            // 使用ConfigXmlOperator获取所有服务ID
            ConfigXmlOperator configXmlOperator = new ConfigXmlOperator(Constant.CONFIG_PATH);

            // 使用新增的getAllServiceIds方法获取所有服务ID
            List<Integer> allServiceIds = configXmlOperator.getAllServiceIds();
            // 将服务ID添加到响应中
            for (Integer id : allServiceIds) {
                serviceIds.add(new ServiceId(id));
            }
            // 设置服务ID列表到响应
            response.setServiceIds(serviceIds);
        } catch (Exception e) {
            logger.error("Error processing GetAllServiceIdsRequest", e);
            // 发生错误时返回空列表
            response.setServiceIds(serviceIds);
        }

        return response;
    }

    /**
     * 处理检查通信状态请求
     * 使用NetworkLinkStatusReader工具类获取网卡状态
     */
    private CheckCommStatusResponse processCheckCommStatusRequest(CheckCommStatusRequest request) {
        logger.info("Processing CheckCommStatusRequest for service: {}, network: {}",
            request.getServiceId(), request.getNetwork());

        // 创建响应对象
        CheckCommStatusResponse response = new CheckCommStatusResponse();
        response.setMessageType(request.getMessageType());
        response.setServiceId(request.getServiceId());
        response.setNetwork(request.getNetwork());

        try {
            // 使用NetworkLinkStatusReader获取网卡状态
            NetworkLinkStatusReader.LinkStatus linkStatus = NetworkLinkStatusReader.getLastLinkStatus();

            if (linkStatus != null) {
                // 设置连接状态
                boolean isConnected = "UP".equals(linkStatus.getStatus());
                response.setIsConnected(isConnected);

                // 设置连接事件时间
                LocalDateTime timestamp = linkStatus.getTimestamp();
                if (timestamp != null) {
                    // 将LocalDateTime转换为毫秒时间戳
                    long epochMilli = timestamp.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
                    response.setConnectionEventTime(new Uint64(new BigInteger(String.valueOf(epochMilli))));
                } else {
                    // 如果时间戳为空，使用当前时间
                    response.setConnectionEventTime(new Uint64(new BigInteger(String.valueOf(System.currentTimeMillis()))));
                }

                logger.info("Network link status: {}, timestamp: {}", linkStatus.getStatus(), timestamp);
            } else {
                // 如果无法获取网卡状态，默认为断开状态
                response.setIsConnected(false);
                response.setConnectionEventTime(new Uint64(new BigInteger(String.valueOf(System.currentTimeMillis()))));
                logger.warn("Unable to get network link status, defaulting to disconnected");
            }

        } catch (Exception e) {
            // 发生异常时，默认为断开状态
            response.setIsConnected(false);
            response.setConnectionEventTime(new Uint64(new BigInteger(String.valueOf(System.currentTimeMillis()))));
            logger.error("Error processing CheckCommStatusRequest", e);
        }

        return response;
    }

    private WorkStatusResponse processWorkStatusRequest(WorkStatusRequest request) {
        logger.info("Processing WorkStatusRequest for service: {}", request.getServiceId());
        WorkStatusResponse response = new WorkStatusResponse();
        response.setMessageType(ContentMessageType.queryWorkStatus);
        response.setServiceId(request.getServiceId());

        try {
            // 获取设备角色（发送端或接收端）
            DeviceConfigReader deviceConfig = DeviceConfigReader.getInstance();
            Network role = deviceConfig.getNetwork();

            // 从/proc/net/dev获取网络统计数据
            Map<String, Long> stats = NetworkStatsReader.getNetworkStats(role);

            // 设置实际数据
            response.setSendPkgNumToday(new Uint32(stats.get("sendPkgNum").intValue()));
            response.setSendPkgSizeToday(new Uint32(stats.get("sendPkgSize").intValue()));
            response.setRecvPkgNumToday(new Uint32(stats.get("recvPkgNum").intValue()));
            response.setRecvPkgSizeToday(new Uint32(stats.get("recvPkgSize").intValue()));

            logger.info("Network stats for {} role: send packets={}, send size={}, recv packets={}, recv size={}",
                    role, stats.get("sendPkgNum"), stats.get("sendPkgSize"),
                    stats.get("recvPkgNum"), stats.get("recvPkgSize"));
        } catch (Exception e) {
            logger.error("Error getting network stats, using default values", e);
            // 出错时使用默认值
            response.setSendPkgNumToday(new Uint32(0));
            response.setSendPkgSizeToday(new Uint32(0));
            response.setRecvPkgNumToday(new Uint32(0));
            response.setRecvPkgSizeToday(new Uint32(0));
        }

        // 设置设备时间
        Uint64 l = new Uint64();
        l.setValue(new BigInteger(String.valueOf(System.currentTimeMillis())));
        response.setDevTime(l);
        return response;
    }

    /**
     * 处理发送包统计请求
     * @param request 发送包统计请求
     * @return 发送包统计响应
     */
    private SendPacketStatsResponse processSendPacketStatsRequest(SendPacketStatsRequest request) {
        logger.info("Processing SendPacketStatsRequest for service: {}, network: {}",
                request.getServiceId(), request.getNetwork());

        // 创建响应对象
        SendPacketStatsResponse response = new SendPacketStatsResponse();
        response.setMessageType(ContentMessageType.sendPacketStats);
        response.setServiceId(request.getServiceId());
        response.setNetwork(request.getNetwork());

        // 如果请求中有period，则设置到响应中
        if (request.hasPeriod()) {
            response.setPeriod(request.getPeriod());
        }

        try {
            // 创建UdpAuditDAO实例
            UdpAuditDAO udpAuditDAO = new UdpAuditDAO();

            // 获取包统计数据
            List<PacketStats> packetStatsList;
            String serviceId = request.getServiceId().intValue()+"";

            if (request.hasPeriod()) {
                // 如果有时间段参数，按时间段查询
                int periodMinutes = request.getPeriod().intValue();
                packetStatsList = udpAuditDAO.getPacketStatsByServiceIdAndPeriod(serviceId, periodMinutes);
                logger.info("Fetched packet stats for service {} with period {} minutes: {} records",
                        serviceId, periodMinutes, packetStatsList.size());
            } else {
                // 否则查询所有数据
                packetStatsList = udpAuditDAO.getPacketStatsByServiceId(serviceId);
                logger.info("Fetched packet stats for service {}: {} records",
                        serviceId, packetStatsList.size());
            }

            // 创建PacketStats集合并设置数据
            SendPacketStatsResponse.PacketStats responsePacketStats = new SendPacketStatsResponse.PacketStats();

            // 将查询结果添加到响应中
            for (com.unimas.asn.servicemanager.servicemanagementhttp.PacketStats stats : packetStatsList) {
                responsePacketStats.add(stats);
            }

            response.setPacketStats(responsePacketStats);

            logger.info("Successfully processed SendPacketStatsRequest for service: {}", serviceId);

        } catch (Exception e) {
            logger.error("Error processing SendPacketStatsRequest for service: {}",
                    request.getServiceId(), e);

            // 发生错误时返回空的统计数据
            SendPacketStatsResponse.PacketStats emptyPacketStats = new SendPacketStatsResponse.PacketStats();
            response.setPacketStats(emptyPacketStats);
        }

        return response;
    }

    /**
     * 处理接收包统计请求
     * @param request 接收包统计请求
     * @return 接收包统计响应
     */
    private ReceivePacketStatsResponse processReceivePacketStatsRequest(ReceivePacketStatsRequest request) {
        logger.info("Processing ReceivePacketStatsRequest for service: {}, network: {}",
                request.getServiceId(), request.getNetwork());

        // 创建响应对象
        ReceivePacketStatsResponse response = new ReceivePacketStatsResponse();
        response.setMessageType(ContentMessageType.receivePacketStats);
        response.setServiceId(request.getServiceId());
        response.setNetwork(request.getNetwork());

        // 如果请求中有period，则设置到响应中
        if (request.hasPeriod()) {
            response.setPeriod(request.getPeriod());
        }

        try {
            // 创建UdpAuditDAO实例
            UdpAuditDAO udpAuditDAO = new UdpAuditDAO();

            // 获取包统计数据
            List<com.unimas.asn.servicemanager.servicemanagementhttp.PacketStats> packetStatsList;
            String serviceId = request.getServiceId().intValue()+"";

            if (request.hasPeriod()) {
                // 如果有时间段参数，按时间段查询
                int periodMinutes = request.getPeriod().intValue();
                packetStatsList = udpAuditDAO.getPacketStatsByServiceIdAndPeriod(serviceId, periodMinutes);
                logger.info("Fetched receive packet stats for service {} with period {} minutes: {} records",
                        serviceId, periodMinutes, packetStatsList.size());
            } else {
                // 否则查询所有数据
                packetStatsList = udpAuditDAO.getPacketStatsByServiceId(serviceId);
                logger.info("Fetched receive packet stats for service {}: {} records",
                        serviceId, packetStatsList.size());
            }

            // 创建PacketStats集合并设置数据
            ReceivePacketStatsResponse.PacketStats responsePacketStats = new ReceivePacketStatsResponse.PacketStats();

            // 将查询结果添加到响应中
            for (com.unimas.asn.servicemanager.servicemanagementhttp.PacketStats stats : packetStatsList) {
                responsePacketStats.add(stats);
            }

            response.setPacketStats(responsePacketStats);

            logger.info("Successfully processed ReceivePacketStatsRequest for service: {}", serviceId);

        } catch (Exception e) {
            logger.error("Error processing ReceivePacketStatsRequest for service: {}",
                    request.getServiceId(), e);

            // 发生错误时返回空的统计数据
            ReceivePacketStatsResponse.PacketStats emptyPacketStats = new ReceivePacketStatsResponse.PacketStats();
            response.setPacketStats(emptyPacketStats);
        }

        return response;
    }

    /**
     * 处理设置网口IP请求（专门用于修改Debian系统网卡地址）
     * @param request 设置网口IP请求
     * @return 设置网口IP响应
     */
    private SetInterfaceIpResponse processSetInterfaceIpRequest(SetInterfaceIpRequest request) {
        logger.info("Processing SetInterfaceIpRequest for interface: {} on Debian system", request.getInterfaceType());

        // 创建响应对象
        SetInterfaceIpResponse response = new SetInterfaceIpResponse();
        response.setMessageType(ContentMessageType.setInterfaceIpService);
        response.setInterfaceType(request.getInterfaceType());

        boolean success = false;

        try {
            // 获取请求中的配置值
            String requestIP = IPUtil.ipAddressToString(request.getIpAddress());
            String requestSubnetMask = IPUtil.ipAddressToString(request.getSubnetMask());
            String requestGateway = request.hasGateway() ? IPUtil.ipAddressToString(request.getGateway()) : null;
            String requestRoute = request.hasIproute() ? IPUtil.ipAddressToString(request.getIproute()) : null;

            logger.info("Requested interface config - IP: {}, SubnetMask: {}, Gateway: {}, Route: {}",
                    requestIP, requestSubnetMask, requestGateway, requestRoute);

            // 根据接口类型处理
            if (request.getInterfaceType() == InterfaceType.management) {
                // 管理口配置 - 修改eth1网卡IP（Debian系统）
                logger.info("Updating management interface (eth1) on Debian system");

                // 在Debian系统上修改eth1网卡配置
                success = updateEth1InterfaceOnDebian(requestIP, requestSubnetMask, requestGateway, requestRoute);

                if (success) {
                    logger.info("Successfully updated management interface (eth1) IP to: {}", requestIP);
                } else {
                    logger.error("Failed to update management interface (eth1) IP");
                }
            } else if (request.getInterfaceType() == InterfaceType.business) {
                // 业务口配置 - 这里可以根据需要实现业务口配置逻辑
                logger.info("Business interface configuration requested, but not implemented yet");
                success = false; // 暂时返回失败
            }

            // 获取当前网卡配置作为响应
            String currentIP = getCurrentEth1IP();
            if (currentIP != null) {
                response.setCurrentIpAddress(IPUtil.createIPAddress(currentIP));
                response.setCurrentSubnetMask(request.getSubnetMask()); // 使用请求中的值
                if (request.hasGateway()) {
                    response.setCurrentGateway(request.getGateway());
                }
                if (request.hasIproute()) {
                    response.setCurIproute(request.getIproute());
                }
            } else {
                // 如果无法获取当前IP，使用请求中的值
                response.setCurrentIpAddress(request.getIpAddress());
                response.setCurrentSubnetMask(request.getSubnetMask());
                if (request.hasGateway()) {
                    response.setCurrentGateway(request.getGateway());
                }
                if (request.hasIproute()) {
                    response.setCurIproute(request.getIproute());
                }
            }

            // 设置执行结果
            response.setResult(success ? 0 : 1);

            logger.info("SetInterfaceIpRequest processed, result: {}", success ? "success" : "failed");

        } catch (Exception e) {
            logger.error("Error processing SetInterfaceIpRequest", e);
            // 设置错误响应
            response.setCurrentIpAddress(request.getIpAddress());
            response.setCurrentSubnetMask(request.getSubnetMask());
            response.setResult(1); // 错误
        }

        // 如果配置修改成功，安排程序在1秒后退出
        if (success && response.getResult() == 0) {
            logger.info("Network interface configuration updated successfully, scheduling application exit in 1 second");
            new Thread(() -> {
                try {
                    Thread.sleep(1000); // 休眠1秒
                    logger.info("Exiting application due to network interface configuration changes");
                    System.exit(0);
                } catch (InterruptedException e) {
                    logger.error("Exit thread interrupted", e);
                    Thread.currentThread().interrupt();
                }
            }).start();
        }

        return response;
    }

    /**
     * 在Debian系统上更新eth1网卡配置
     * @param ip IP地址
     * @param subnetMask 子网掩码
     * @param gateway 网关（可选）
     * @param route 路由（可选）
     * @return 是否成功
     */
    private boolean updateEth1InterfaceOnDebian(String ip, String subnetMask, String gateway, String route) {
        try {
            logger.info("Updating eth1 interface on Debian - IP: {}, SubnetMask: {}, Gateway: {}, Route: {}",
                    ip, subnetMask, gateway, route);

            // 1. 使用ip命令临时设置网卡IP
            ProcessBuilder pb1 = new ProcessBuilder("ip", "addr", "flush", "dev", "eth1");
            Process process1 = pb1.start();
            int exitCode1 = process1.waitFor();

            if (exitCode1 != 0) {
                logger.error("Failed to flush eth1 interface, exit code: {}", exitCode1);
                return false;
            }

            // 2. 计算网络前缀长度（从子网掩码）
            int prefixLength = calculatePrefixLength(subnetMask);

            // 3. 设置新的IP地址
            ProcessBuilder pb2 = new ProcessBuilder("ip", "addr", "add", ip + "/" + prefixLength, "dev", "eth1");
            Process process2 = pb2.start();
            int exitCode2 = process2.waitFor();

            if (exitCode2 != 0) {
                logger.error("Failed to set IP address for eth1, exit code: {}", exitCode2);
                return false;
            }

            // 4. 启用网卡
            ProcessBuilder pb3 = new ProcessBuilder("ip", "link", "set", "eth1", "up");
            Process process3 = pb3.start();
            int exitCode3 = process3.waitFor();

            if (exitCode3 != 0) {
                logger.error("Failed to bring up eth1 interface, exit code: {}", exitCode3);
                return false;
            }

            // 5. 设置网关（如果提供）
            if (gateway != null && !gateway.isEmpty()) {
                ProcessBuilder pb4 = new ProcessBuilder("ip", "route", "add", "default", "via", gateway, "dev", "eth1");
                Process process4 = pb4.start();
                int exitCode4 = process4.waitFor();

                if (exitCode4 != 0) {
                    logger.warn("Failed to set gateway for eth1, exit code: {} (this may be normal if gateway already exists)", exitCode4);
                }
            }

            logger.info("Successfully updated eth1 interface configuration");
            return true;

        } catch (Exception e) {
            logger.error("Error updating eth1 interface on Debian", e);
            return false;
        }
    }

    /**
     * 从子网掩码计算网络前缀长度
     * @param subnetMask 子网掩码（如*************）
     * @return 前缀长度（如24）
     */
    private int calculatePrefixLength(String subnetMask) {
        try {
            String[] parts = subnetMask.split("\\.");
            int prefixLength = 0;

            for (String part : parts) {
                int octet = Integer.parseInt(part);
                prefixLength += Integer.bitCount(octet);
            }

            return prefixLength;
        } catch (Exception e) {
            logger.error("Error calculating prefix length from subnet mask: {}", subnetMask, e);
            return 24; // 默认返回24
        }
    }

    /**
     * 获取当前eth1网卡的IP地址
     * @return IP地址字符串
     */
    private String getCurrentEth1IP() {
        try {
            ProcessBuilder pb = new ProcessBuilder("ip", "addr", "show", "eth1");
            Process process = pb.start();

            try (java.io.BufferedReader reader = new java.io.BufferedReader(
                    new java.io.InputStreamReader(process.getInputStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    if (line.contains("inet ") && !line.contains("inet6")) {
                        // 解析IP地址，格式如：inet *************/24 brd ************* scope global eth1
                        String[] parts = line.trim().split("\\s+");
                        for (int i = 0; i < parts.length; i++) {
                            if ("inet".equals(parts[i]) && i + 1 < parts.length) {
                                String ipWithPrefix = parts[i + 1];
                                if (ipWithPrefix.contains("/")) {
                                    return ipWithPrefix.split("/")[0];
                                }
                                return ipWithPrefix;
                            }
                        }
                    }
                }
            }

            process.waitFor();
        } catch (Exception e) {
            logger.error("Error getting current eth1 IP", e);
        }

        return null;
    }

    /**
     * 处理查询上位机管理请求
     * @param request 查询上位机管理请求
     * @return 查询上位机管理响应
     */
    private QueryHostMngResponse processQueryHostMngRequest(QueryHostMngRequest request) {
        logger.info("Processing QueryHostMngRequest");

        // 创建响应对象
        QueryHostMngResponse response = new QueryHostMngResponse();
        response.setMessageType(ContentMessageType.hostMngService);

        try {
            // 从配置文件读取上位机配置
            Properties centerConfig = SystemConfigManager.getCurrentCenterConfig();
            String centerIP = centerConfig.getProperty("alarm.report.destination.ip", "127.0.0.1");
            String authPortStr = centerConfig.getProperty("auth.port", "8080");
            String alarmPortStr = centerConfig.getProperty("alarm.report.destination.port", "8080");
            String certMngPortStr = centerConfig.getProperty("server.port", "8080");
            String sgPortStr = centerConfig.getProperty("server.port", "8080");

            // 设置响应数据
            response.setCenterIP(IPUtil.createIPAddress(centerIP));
            response.setAuthPort(new PortNumber(Integer.parseInt(authPortStr)));
            response.setAlarmPort(new PortNumber(Integer.parseInt(alarmPortStr)));
            response.setCertMngPort(new PortNumber(Integer.parseInt(certMngPortStr)));
            response.setSgPort(new PortNumber(Integer.parseInt(sgPortStr)));

            logger.info("QueryHostMngRequest processed successfully - CenterIP: {}, AuthPort: {}, AlarmPort: {}, CertMngPort: {}, SgPort: {}",
                    centerIP, authPortStr, alarmPortStr, certMngPortStr, sgPortStr);

        } catch (Exception e) {
            logger.error("Error processing QueryHostMngRequest", e);
            // 设置默认值
            try {
                response.setCenterIP(IPUtil.createIPAddress("127.0.0.1"));
                response.setAuthPort(new PortNumber(8080));
                response.setAlarmPort(new PortNumber(8080));
                response.setCertMngPort(new PortNumber(8080));
                response.setSgPort(new PortNumber(8080));
            } catch (Exception ex) {
                logger.error("Error setting default values for QueryHostMngResponse", ex);
            }
        }

        return response;
    }

    /**
     * 处理设置上位机管理请求
     * @param request 设置上位机管理请求
     * @return 设置上位机管理响应
     */
    private SetHostMngResponse processSetHostMngRequest(SetHostMngRequest request) {
        logger.info("Processing SetHostMngRequest");

        // 创建响应对象
        SetHostMngResponse response = new SetHostMngResponse();
        response.setMessageType(ContentMessageType.hostMngService);

        boolean allSuccess = true;
        StringBuilder statusDescription = new StringBuilder();

        try {
            // 读取当前配置
            Properties props = SystemConfigManager.getCurrentCenterConfig();

            // 更新配置（只更新提供的字段）
            if (request.hasCenterIP()) {
                String centerIP = IPUtil.ipAddressToString(request.getCenterIP());
                props.setProperty("alarm.report.destination.ip", centerIP);
                response.setCenterIP(request.getCenterIP());
                logger.info("Updated centerIP to: {}", centerIP);
            } else {
                // 使用当前值
                String currentCenterIP = props.getProperty("alarm.report.destination.ip", "127.0.0.1");
                response.setCenterIP(IPUtil.createIPAddress(currentCenterIP));
            }

            if (request.hasAuthPort()) {
                int authPort = request.getAuthPort().intValue();
                props.setProperty("auth.port", String.valueOf(authPort));
                response.setAuthPort(request.getAuthPort());
                logger.info("Updated authPort to: {}", authPort);
            } else {
                int currentAuthPort = Integer.parseInt(props.getProperty("auth.port", "8080"));
                response.setAuthPort(new PortNumber(currentAuthPort));
            }

            if (request.hasAlarmPort()) {
                int alarmPort = request.getAlarmPort().intValue();
                props.setProperty("alarm.report.destination.port", String.valueOf(alarmPort));
                response.setAlarmPort(request.getAlarmPort());
                logger.info("Updated alarmPort to: {}", alarmPort);
            } else {
                int currentAlarmPort = Integer.parseInt(props.getProperty("alarm.report.destination.port", "8080"));
                response.setAlarmPort(new PortNumber(currentAlarmPort));
            }

            if (request.hasCertMngPort()) {
                int certMngPort = request.getCertMngPort().intValue();
                props.setProperty("server.port", String.valueOf(certMngPort));
                response.setCertMngPort(request.getCertMngPort());
                logger.info("Updated certMngPort to: {}", certMngPort);
            } else {
                int currentCertMngPort = Integer.parseInt(props.getProperty("server.port", "8080"));
                response.setCertMngPort(new PortNumber(currentCertMngPort));
            }

            if (request.hasSgPort()) {
                int sgPort = request.getSgPort().intValue();
                props.setProperty("server.port", String.valueOf(sgPort));
                response.setSgPort(request.getSgPort());
                logger.info("Updated sgPort to: {}", sgPort);
            } else {
                int currentSgPort = Integer.parseInt(props.getProperty("server.port", "8080"));
                response.setSgPort(new PortNumber(currentSgPort));
            }

            // 保存配置到文件
            try (OutputStream os = new FileOutputStream("/etc/unimas/tomcat/conf/application.properties")) {
                props.store(os, "Updated by SetHostMngRequest");
                logger.info("Successfully saved host management configuration");
            }

            // 设置执行结果
            response.setResult(0); // 成功

            logger.info("SetHostMngRequest processed successfully");

        } catch (Exception e) {
            logger.error("Error processing SetHostMngRequest", e);
            allSuccess = false;
            response.setResult(1); // 失败

            // 设置默认响应值
            try {
                response.setCenterIP(IPUtil.createIPAddress("127.0.0.1"));
                response.setAuthPort(new PortNumber(8080));
                response.setAlarmPort(new PortNumber(8080));
                response.setCertMngPort(new PortNumber(8080));
                response.setSgPort(new PortNumber(8080));
            } catch (Exception ex) {
                logger.error("Error setting default values for SetHostMngResponse", ex);
            }
        }

        // 如果有配置变更，安排程序退出
        if (allSuccess) {
            logger.info("Host management configuration updated successfully, scheduling application exit in 1 second");
            // 在新线程中执行延迟退出，避免阻塞响应
            new Thread(() -> {
                try {
                    Thread.sleep(1000); // 休眠1秒
                    logger.info("Exiting application due to host management configuration changes");
                    System.exit(0);
                } catch (InterruptedException e) {
                    logger.error("Exit thread interrupted", e);
                    Thread.currentThread().interrupt();
                }
            }).start();
        }

        return response;
    }

    /**
     * 处理查询源设备请求（查询服务配置中的源设备信息）
     * @param request 查询源设备请求
     * @return 查询源设备响应
     */
    private QuerySourceDeviceResponse processQuerySourceDeviceRequest(QuerySourceDeviceRequest request) {
        logger.info("Processing QuerySourceDeviceRequest for service: {} - querying source devices from config", request.getServiceId());

        // 创建响应对象
        QuerySourceDeviceResponse response = new QuerySourceDeviceResponse();
        response.setMessageType(ContentMessageType.sourceDeviceService);
        response.setServiceId(request.getServiceId());

        try {
            String serviceId = request.getServiceId().intValue() + "";

            // 使用ServiceXmlOperator获取源设备信息
            ServiceXmlOperator serviceXmlOperator = new ServiceXmlOperator(serviceId);
            List<SourceDevice> sourceDevices = serviceXmlOperator.getSourceDevices();

            // 创建设备列表
            QuerySourceDeviceResponse.CurrentDevices currentDevices = new QuerySourceDeviceResponse.CurrentDevices();

            // 将源设备添加到响应中
            for (SourceDevice device : sourceDevices) {
                currentDevices.add(device);
            }

            response.setCurrentDevices(currentDevices);

            logger.info("QuerySourceDeviceRequest processed successfully for service: {}, found {} devices",
                    request.getServiceId(), sourceDevices.size());

        } catch (Exception e) {
            logger.error("Error processing QuerySourceDeviceRequest for service: {}", request.getServiceId(), e);
            // 返回空的设备列表
            QuerySourceDeviceResponse.CurrentDevices emptyDevices = new QuerySourceDeviceResponse.CurrentDevices();
            response.setCurrentDevices(emptyDevices);
        }

        return response;
    }

    /**
     * 处理设置源设备请求（修改服务配置中的源设备信息）
     * @param request 设置源设备请求
     * @return 设置源设备响应
     */
    private SetSourceDeviceResponse processSetSourceDeviceRequest(SetSourceDeviceRequest request) {
        logger.info("Processing SetSourceDeviceRequest for service: {} - updating source devices in config", request.getServiceId());

        // 创建响应对象
        SetSourceDeviceResponse response = new SetSourceDeviceResponse();
        response.setMessageType(ContentMessageType.sourceDeviceService);
        response.setServiceId(request.getServiceId());

        boolean success = false;

        try {
            String serviceId = request.getServiceId().intValue() + "";

            // 校验服务是否正在运行，运行中的服务不允许修改源设备配置
            if (isServiceRunning(serviceId)) {
                logger.warn("Service {} is currently running, cannot modify source device configuration", serviceId);
                response.setResult(1); // 失败

                // 返回当前的源设备列表（不做修改）
                ServiceXmlOperator serviceXmlOperator = new ServiceXmlOperator(serviceId);
                List<SourceDevice> currentSourceDevices = serviceXmlOperator.getSourceDevices();

                SetSourceDeviceResponse.CurrentDevices currentDevices = new SetSourceDeviceResponse.CurrentDevices();
                for (SourceDevice device : currentSourceDevices) {
                    currentDevices.add(device);
                }
                response.setCurrentDevices(currentDevices);

                logger.info("SetSourceDeviceRequest rejected - service {} is running", serviceId);
                return response;
            }

            // 获取源设备列表
            SetSourceDeviceRequest.Sourcedevices sourcedevices = request.getSourcedevices();

            if (sourcedevices != null) {
                logger.info("Updating source devices for service: {}, device count: {}", serviceId, sourcedevices.size());

                // 将请求中的源设备转换为List<SourceDevice>
                List<SourceDevice> sourceDeviceList = new ArrayList<>();
                for (int i = 0; i < sourcedevices.size(); i++) {
                    sourceDeviceList.add(sourcedevices.get(i));
                }

                // 使用ServiceXmlOperator更新源设备配置
                ServiceXmlOperator serviceXmlOperator = new ServiceXmlOperator(serviceId);
                success = serviceXmlOperator.updateSourceDevices(sourceDeviceList);

                if (success) {
                    logger.info("Successfully updated source devices for service: {}", serviceId);
                } else {
                    logger.error("Failed to update source devices for service: {}", serviceId);
                }
            } else {
                logger.warn("No source devices provided for service: {}", serviceId);
                success = true; // 空列表也算成功
            }

            // 创建响应中的当前设备列表
            SetSourceDeviceResponse.CurrentDevices currentDevices = new SetSourceDeviceResponse.CurrentDevices();

            // 将请求中的设备复制到响应中
            if (sourcedevices != null) {
                for (int i = 0; i < sourcedevices.size(); i++) {
                    currentDevices.add(sourcedevices.get(i));
                }
            }

            response.setCurrentDevices(currentDevices);
            response.setResult(success ? 0 : 1);

            logger.info("SetSourceDeviceRequest processed, result: {}", success ? "success" : "failed");

        } catch (Exception e) {
            logger.error("Error processing SetSourceDeviceRequest for service: {}", request.getServiceId(), e);
            success = false;
            response.setResult(1); // 失败

            // 返回空的设备列表
            SetSourceDeviceResponse.CurrentDevices emptyDevices = new SetSourceDeviceResponse.CurrentDevices();
            response.setCurrentDevices(emptyDevices);
        }

        return response;
    }

    /**
     * 检查服务是否正在运行
     * @param serviceId 服务ID
     * @return 如果服务正在运行返回true，否则返回false
     */
    private boolean isServiceRunning(String serviceId) {
        try {
            // 使用现有的服务状态查询逻辑
            ConfigXmlOperator configXmlOperator = new ConfigXmlOperator(Constant.CONFIG_PATH);
            boolean running = configXmlOperator.getIsRun(serviceId);

            logger.debug("Service {} , running: {}", serviceId, running);
            return running;

        } catch (Exception e) {
            logger.error("Error checking service status for service: {}", serviceId, e);
            // 出现异常时，为了安全起见，假设服务正在运行，不允许修改
            return true;
        }
    }

    private void sendHttpErrorResponse(HttpServletResponse response, int errorCode, String errorMessage) {
        try {
            // 如果是401未授权错误，直接返回纯文本响应
            if (errorCode == HttpServletResponse.SC_UNAUTHORIZED) {
                response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                response.setContentType("text/plain;charset=UTF-8");
                response.getWriter().write("授权已过期，请联系管理员");
                return;
            }

            // 其他错误返回ASN.1格式的错误响应
            response.setContentType("application/octet-stream");

            MessageResponseFrame responseFrame = new MessageResponseFrame();
            responseFrame.setVersion(new Uint8(1));
            responseFrame.setContent(new MessageResponseFrame.Content());

            // 创建错误响应对象
            ErrorResponse errorResponse = new ErrorResponse();
            errorResponse.setErrorState(ProcessErrorState.messageStructureError);
            errorResponse.setMessageType(ContentMessageType.queryServiceStatus);
            // 设置错误响应
            responseFrame.getContent().setError(errorResponse);

            byte[] responseData = OssServAdapter.encode(responseFrame);
            response.setContentLength(responseData.length);
            try (OutputStream os = response.getOutputStream()) {
                os.write(responseData);
                os.flush();
            }

        } catch (Exception e) {
            logger.error("发送错误响应失败", e);
        }
    }

}